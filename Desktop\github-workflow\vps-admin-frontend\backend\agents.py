"""
Specialized AI agents for the VPS Admin Orchestrator system.
Contains individual agent classes for different tasks.
"""

import asyncio
import json
import re
import traceback
from typing import List, Dict, Any, Optional, Union, Tuple
from abc import ABC, abstractmethod

import google.generativeai as genai

from config import Config
from models import (
    AgentResponse, PlannerResponse, ExecutorResponse,
    RefinerResponse, SummarizerResponse, ErrorRecoveryResponse, SSHResult
)


def clean_and_parse_json(response_text: str) -> Tuple[Union[dict, list], str]:
    """
    Robust JSON parsing that handles markdown code blocks and escape characters.

    Returns:
        tuple: (parsed_data, cleaned_response)

    Raises:
        json.JSONDecodeError: If JSON parsing fails after all cleaning attempts
    """
    if not response_text or not response_text.strip():
        raise json.JSONDecodeError("Empty response", "", 0)

    # Step 1: Remove markdown code blocks
    cleaned_response = response_text.strip()

    # Handle various markdown patterns
    markdown_patterns = [
        (r'^```json\s*\n?', ''),  # ```json at start
        (r'^```\s*\n?', ''),     # ``` at start
        (r'\n?```\s*$', ''),     # ``` at end
        (r'^`{1,2}', ''),        # Single or double backticks at start
        (r'`{1,2}$', ''),        # Single or double backticks at end
    ]

    for pattern, replacement in markdown_patterns:
        cleaned_response = re.sub(pattern, replacement, cleaned_response, flags=re.MULTILINE)

    cleaned_response = cleaned_response.strip()

    # Step 2: Handle common escape character issues
    # Fix common problematic escape sequences that might appear in AI responses
    escape_fixes = [
        (r'\\(?!["\\/bfnrt])', r'\\\\'),  # Fix single backslashes that aren't valid escapes
        (r'(?<!\\)\\(?=\s)', r'\\\\'),    # Fix trailing backslashes before whitespace
    ]

    for pattern, replacement in escape_fixes:
        cleaned_response = re.sub(pattern, replacement, cleaned_response)

    # Step 3: Try to parse JSON
    try:
        parsed_data = json.loads(cleaned_response)
        return parsed_data, cleaned_response
    except json.JSONDecodeError as e:
        # Step 4: Additional cleaning for common issues
        # Remove any remaining non-JSON content before/after the JSON
        json_match = re.search(r'(\[.*\]|\{.*\})', cleaned_response, re.DOTALL)
        if json_match:
            json_only = json_match.group(1)
            try:
                parsed_data = json.loads(json_only)
                return parsed_data, json_only
            except json.JSONDecodeError:
                pass

        # If all else fails, re-raise the original error
        raise e


class BaseAgent(ABC):
    """Base class for all specialized agents."""

    def __init__(self, config: Config, model_name: Optional[str] = None):
        self.config = config
        self.model_name = model_name or config.GEMINI_MODEL_NAME
        self.model = None
        self.ai_client = None
        self._initialize_model()
        self._initialize_ai_client()

    def _initialize_model(self):
        """Initialize the AI model for this agent."""
        try:
            genai.configure(api_key=self.config.GEMINI_API_KEY)
            self.model = genai.GenerativeModel(self.model_name)
            print(f"Initialized {self.__class__.__name__} with model: {self.model_name}")
        except Exception as e:
            print(f"FATAL: Error initializing {self.__class__.__name__}: {e}")
            raise

    def _initialize_ai_client(self):
        """Initialize the AI client for thinking budget functionality."""
        try:
            # Import here to avoid circular imports
            from ai_client import AIClient
            self.ai_client = AIClient(self.config)
            print(f"Initialized AI client for {self.__class__.__name__}")
        except Exception as e:
            print(f"WARNING: Could not initialize AI client for {self.__class__.__name__}: {e}")
            self.ai_client = None

    @abstractmethod
    async def process(self, **kwargs) -> AgentResponse:
        """Process the agent's specific task."""
        pass

    async def _generate_response(self, prompt: str, timeout: int = 60) -> str:
        """Generate response from the AI model."""
        try:
            response = await asyncio.wait_for(
                asyncio.to_thread(self.model.generate_content, prompt),
                timeout=timeout
            )

            if not response or not hasattr(response, 'text'):
                raise ValueError("AI response was empty or blocked")

            return response.text.strip()
        except asyncio.TimeoutError:
            raise TimeoutError(f"AI call timed out after {timeout} seconds")
        except Exception as e:
            print(f"ERROR in {self.__class__.__name__}: {e}")
            raise


class PlannerAgent(BaseAgent):
    """Agent responsible for breaking down user requests into executable steps."""

    async def process(self, user_prompt: str, system_info: str, percentage: int = 70, **kwargs) -> PlannerResponse:
        """Break down user request into a structured plan with commands."""

        prompt = f"""Break down this request into executable steps with commands.

System: {system_info}
Request: "{user_prompt}"

Return JSON array with "description" and "command" for each step:
[
  {{"description": "Update packages", "command": "sudo apt update"}},
  {{"description": "Install nginx", "command": "sudo apt install -y nginx"}}
]"""

        try:
            # Use thinking budget for complex tasks (percentage > 50%)
            if (percentage > self.config.MIN_THINKING_PERCENTAGE and
                self.ai_client and
                hasattr(self.ai_client, 'generate_response_with_thinking')):
                print(f"[PlannerAgent] Using thinking budget for {percentage}% difficulty task")
                response_text = await asyncio.wait_for(
                    self.ai_client.generate_response_with_thinking(
                        task_id="planner",
                        prompt=prompt,
                        percentage=percentage
                    ),
                    timeout=150  # 2.5 minutes timeout for thinking generation
                )
            else:
                print(f"[PlannerAgent] Using regular generation for {percentage}% difficulty task")
                response_text = await asyncio.wait_for(
                    self._generate_response(prompt),
                    timeout=90  # 1.5 minutes timeout for regular generation
                )

            # Check if response is empty
            if not response_text or not response_text.strip():
                raise ValueError("AI response was empty")

            # Parse JSON response using robust parsing function
            try:
                # Additional debug logging
                print(f"[PlannerAgent] Raw response length: {len(response_text)}")
                print(f"[PlannerAgent] First 100 chars of raw response: {response_text[:100]}")

                plan_data, cleaned_response = clean_and_parse_json(response_text)

                print(f"[PlannerAgent] Cleaned response length: {len(cleaned_response)}")
                print(f"[PlannerAgent] First 100 chars of cleaned response: {cleaned_response[:100]}")

                if not isinstance(plan_data, list):
                    raise ValueError("Response is not a JSON array")

                # Validate structure
                for i, step in enumerate(plan_data):
                    if not isinstance(step, dict) or 'description' not in step:
                        raise ValueError(f"Step {i} missing 'description' field")
                    if 'command' not in step:
                        raise ValueError(f"Step {i} missing 'command' field")

                print(f"[PlannerAgent] Successfully parsed {len(plan_data)} steps")
                return PlannerResponse(
                    content=response_text,
                    plan_steps=plan_data,
                    estimated_steps=len(plan_data),
                    timestamp=asyncio.get_event_loop().time(),
                    metadata={
                        "user_prompt": user_prompt,
                        "total_steps": len(plan_data),
                        "task_type": "general"
                    }
                )

            except (json.JSONDecodeError, ValueError) as e:
                print(f"ERROR: Planner returned invalid JSON: {e}")
                print(f"Raw response: {response_text}")

                # Try to get cleaned response for debugging
                try:
                    _, cleaned_response = clean_and_parse_json(response_text)
                    print(f"Cleaned response: {cleaned_response}")
                except:
                    print("Could not clean response for debugging")

                # Fallback: create a simple single-step plan
                fallback_plan = [{"description": f"Execute user request: {user_prompt}", "command": "echo 'Please provide specific commands for this task'"}]
                return PlannerResponse(
                    content=json.dumps(fallback_plan),
                    plan_steps=fallback_plan,
                    estimated_steps=1,
                    timestamp=asyncio.get_event_loop().time(),
                    success=False,
                    metadata={
                        "error": f"JSON parsing failed: {str(e)}",
                        "raw_response": response_text
                    }
                )

        except asyncio.TimeoutError:
            print(f"ERROR: PlannerAgent timed out")
            # Create a fallback plan for timeout
            fallback_plan = [{"description": f"Execute user request: {user_prompt}", "command": "echo 'Planning timed out - please provide specific commands'"}]
            return PlannerResponse(
                content=json.dumps(fallback_plan),
                plan_steps=fallback_plan,
                estimated_steps=1,
                timestamp=asyncio.get_event_loop().time(),
                success=False,
                metadata={
                    "error": "PlannerAgent timed out",
                    "exception_type": "TimeoutError"
                }
            )

        except Exception as e:
            print(f"ERROR in PlannerAgent: {e}")
            print(f"[PlannerAgent] Exception type: {type(e).__name__}")

            # Create a fallback plan even for general exceptions
            fallback_plan = [{"description": f"Execute user request: {user_prompt}", "command": "echo 'Please provide specific commands for this task'"}]
            return PlannerResponse(
                content=json.dumps(fallback_plan),
                plan_steps=fallback_plan,
                estimated_steps=1,
                timestamp=asyncio.get_event_loop().time(),
                success=False,
                metadata={
                    "error": f"PlannerAgent exception: {str(e)}",
                    "exception_type": type(e).__name__
                }
            )


class ExecutorAgent(BaseAgent):
    """Agent responsible for converting plan steps into specific shell commands."""

    async def process(self, step_description: str, context_history: List[str],
                     system_info: str, **kwargs) -> ExecutorResponse:
        """Convert a plan step into a specific shell command."""

        context_str = "\n".join([f"- {ctx}" for ctx in context_history]) if context_history else "None"

        prompt = f"""Generate ONE shell command for this objective.

System: {system_info}
Context: {context_str}
Objective: "{step_description}"

Return only the command (non-interactive, use sudo if needed):"""

        try:
            command = await self._generate_response(prompt)

            # Basic security analysis
            dangerous_patterns = [
                'rm -rf /', 'chmod 777', 'chown -R root', 'iptables -F',
                'ufw disable', 'systemctl stop ssh', 'passwd -d', 'sudo su -'
            ]

            security_risk = any(pattern in command.lower() for pattern in dangerous_patterns)

            # Determine command type
            command_type = "unknown"
            if any(pkg_cmd in command.lower() for pkg_cmd in ['apt', 'yum', 'dnf', 'install']):
                command_type = "package_management"
            elif any(svc_cmd in command.lower() for svc_cmd in ['systemctl', 'service']):
                command_type = "service_management"
            elif any(file_cmd in command.lower() for file_cmd in ['cp', 'mv', 'rm', 'chmod', 'chown']):
                command_type = "file_management"
            elif any(net_cmd in command.lower() for net_cmd in ['curl', 'wget', 'ssh', 'scp']):
                command_type = "network"

            return ExecutorResponse(
                content=command,
                command=command,
                command_type=command_type,
                security_risk=security_risk,
                timestamp=asyncio.get_event_loop().time(),
                metadata={
                    "step_description": step_description,
                    "context_items": len(context_history)
                }
            )

        except Exception as e:
            print(f"ERROR in ExecutorAgent: {e}")
            return ExecutorResponse(
                content=f"Error: {str(e)}",
                command="",
                timestamp=asyncio.get_event_loop().time(),
                success=False,
                metadata={"error": str(e)}
            )


class RefinerAgent(BaseAgent):
    """Agent responsible for analyzing failures and suggesting corrections."""

    async def process(self, step_description: str, failed_attempts: List[Dict[str, Any]],
                     **kwargs) -> RefinerResponse:
        """Analyze failed attempts and suggest a corrected command."""

        attempts_str = ""
        for i, attempt in enumerate(failed_attempts, 1):
            command = attempt.get('command', 'Unknown')
            error = attempt.get('error_message', 'Unknown error')
            stdout = attempt.get('stdout', '')
            stderr = attempt.get('stderr', '')
            exit_status = attempt.get('exit_status', 'Unknown')

            attempts_str += f"Attempt {i}:\n"
            attempts_str += f"- Command: {command}\n"
            attempts_str += f"- Exit Status: {exit_status}\n"
            attempts_str += f"- Error Message: {error}\n"

            if stdout and stdout.strip():
                stdout_preview = stdout[:300] + "..." if len(stdout) > 300 else stdout
                attempts_str += f"- Standard Output:\n{stdout_preview}\n"

            if stderr and stderr.strip():
                stderr_preview = stderr[:300] + "..." if len(stderr) > 300 else stderr
                attempts_str += f"- Standard Error:\n{stderr_preview}\n"

            attempts_str += "\n"

        prompt = f"""Fix this failed command with an alternative approach.

Objective: "{step_description}"
Failed attempts:
{attempts_str}

Return only the corrected command:"""

        try:
            corrected_command = await self._generate_response(prompt)

            # Extract original command from the most recent attempt
            original_command = failed_attempts[-1].get('command', '') if failed_attempts else ''

            # Generate analysis
            analysis = f"Analyzed {len(failed_attempts)} failed attempts with stdout/stderr details and suggested alternative approach to achieve the objective"

            return RefinerResponse(
                content=corrected_command,
                original_command=original_command,
                corrected_command=corrected_command,
                analysis=analysis,
                timestamp=asyncio.get_event_loop().time(),
                metadata={
                    "step_description": step_description,
                    "attempts_analyzed": len(failed_attempts)
                }
            )

        except Exception as e:
            print(f"ERROR in RefinerAgent: {e}")
            return RefinerResponse(
                content=f"Error: {str(e)}",
                original_command="",
                corrected_command="",
                analysis=f"Failed to analyze: {str(e)}",
                timestamp=asyncio.get_event_loop().time(),
                success=False,
                metadata={"error": str(e)}
            )


class SummarizerAgent(BaseAgent):
    """Agent responsible for creating concise summaries of completed steps."""

    async def process(self, step_description: str, command: str, ssh_result: SSHResult,
                     **kwargs) -> SummarizerResponse:
        """Create a concise summary of a completed step."""

        stdout_preview = ssh_result.stdout[:200] + "..." if len(ssh_result.stdout) > 200 else ssh_result.stdout
        stderr_preview = ssh_result.stderr[:200] + "..." if len(ssh_result.stderr) > 200 else ssh_result.stderr

        prompt = f"""Summarize what was accomplished in 1-2 sentences.

Step: "{step_description}"
Command: {command}
Success: {ssh_result.success}
Output: {stdout_preview}
Errors: {stderr_preview}

Summary:"""

        try:
            summary = await self._generate_response(prompt)

            # Extract key outcomes from the result
            key_outcomes = []
            if ssh_result.success:
                if "installed" in ssh_result.stdout.lower():
                    key_outcomes.append("package_installed")
                if "started" in ssh_result.stdout.lower() or "active" in ssh_result.stdout.lower():
                    key_outcomes.append("service_started")
                if "enabled" in ssh_result.stdout.lower():
                    key_outcomes.append("service_enabled")
                if "created" in ssh_result.stdout.lower():
                    key_outcomes.append("file_created")

            return SummarizerResponse(
                content=summary,
                step_summary=summary,
                key_outcomes=key_outcomes,
                timestamp=asyncio.get_event_loop().time(),
                metadata={
                    "step_description": step_description,
                    "command": command,
                    "success": ssh_result.success,
                    "exit_code": ssh_result.exit_status
                }
            )

        except Exception as e:
            print(f"ERROR in SummarizerAgent: {e}")
            fallback_summary = f"Step completed: {step_description}"
            return SummarizerResponse(
                content=fallback_summary,
                step_summary=fallback_summary,
                key_outcomes=[],
                timestamp=asyncio.get_event_loop().time(),
                success=False,
                metadata={"error": str(e)}
            )


class ErrorRecoveryPlanner(BaseAgent):
    """Specialized agent for creating recovery plans when commands fail."""

    async def process(self, failed_command: str, error_details: Dict[str, Any],
                     original_objective: str, **kwargs) -> ErrorRecoveryResponse:
        """Create a recovery plan to fix a failed command and achieve the original objective."""

        stdout = error_details.get('stdout', '')
        stderr = error_details.get('stderr', '')
        exit_status = error_details.get('exit_status', 'Unknown')

        # Truncate long outputs for the prompt
        stdout_preview = stdout[:500] + "..." if len(stdout) > 500 else stdout
        stderr_preview = stderr[:500] + "..." if len(stderr) > 500 else stderr

        prompt = f"""Create recovery steps to fix this failed command.

Objective: "{original_objective}"
Failed: {failed_command}
Exit: {exit_status}
Output: {stdout_preview}
Error: {stderr_preview}

Return JSON array with recovery steps:
[
  {{"description": "Fix description", "command": "fix command"}},
  {{"description": "Next step", "command": "next command"}}
]"""

        try:
            response_text = await self._generate_response(prompt)

            # Parse JSON response using robust parsing function
            try:
                print(f"[ErrorRecoveryPlanner] Raw response length: {len(response_text)}")
                print(f"[ErrorRecoveryPlanner] First 100 chars of raw response: {response_text[:100]}")

                recovery_plan, cleaned_response = clean_and_parse_json(response_text)

                print(f"[ErrorRecoveryPlanner] Cleaned response length: {len(cleaned_response)}")
                print(f"[ErrorRecoveryPlanner] First 100 chars of cleaned response: {cleaned_response[:100]}")

                if not isinstance(recovery_plan, list):
                    raise ValueError("Response is not a JSON array")

                # Validate structure
                for i, step in enumerate(recovery_plan):
                    if not isinstance(step, dict) or 'description' not in step or 'command' not in step:
                        raise ValueError(f"Recovery step {i} missing required fields")

                # Determine recovery approach based on error analysis
                recovery_approach = "unknown"
                error_text = (stderr + " " + stdout).lower()
                if "already exists" in error_text:
                    recovery_approach = "cleanup_and_retry"
                elif "permission denied" in error_text:
                    recovery_approach = "fix_permissions"
                elif ("not found" in error_text or "no such" in error_text or
                      "unable to locate package" in error_text or "package not available" in error_text):
                    recovery_approach = "install_dependencies"
                elif "connection" in error_text or "network" in error_text:
                    recovery_approach = "network_alternative"
                elif ("systemd" in error_text or "systemctl" in error_text or
                      "init system" in error_text or "can't operate" in error_text or
                      "failed to connect to bus" in error_text):
                    recovery_approach = "service_management_alternative"
                elif ("apache" in error_text and ("could not reliably determine" in error_text or
                      "ah00558" in error_text or "servername" in error_text)):
                    recovery_approach = "apache_configuration_fix"
                elif ("apache" in error_text and ("syntax" in error_text or "config" in error_text)):
                    recovery_approach = "apache_syntax_fix"
                elif "port already in use" in error_text or "address already in use" in error_text:
                    recovery_approach = "port_conflict_resolution"
                else:
                    recovery_approach = "alternative_method"

                print(f"[ErrorRecoveryPlanner] Created recovery plan with {len(recovery_plan)} steps")
                return ErrorRecoveryResponse(
                    content=response_text,
                    recovery_plan=recovery_plan,
                    recovery_approach=recovery_approach,
                    estimated_recovery_steps=len(recovery_plan),
                    timestamp=asyncio.get_event_loop().time(),
                    metadata={
                        "failed_command": failed_command,
                        "original_objective": original_objective,
                        "error_type": recovery_approach,
                        "total_recovery_steps": len(recovery_plan)
                    }
                )

            except (json.JSONDecodeError, ValueError) as e:
                print(f"ERROR: ErrorRecoveryPlanner returned invalid JSON: {e}")
                print(f"Raw response: {response_text}")

                # Try to get cleaned response for debugging
                try:
                    _, cleaned_response = clean_and_parse_json(response_text)
                    print(f"Cleaned response: {cleaned_response}")
                except:
                    print("Could not clean response for debugging")

                # Fallback: create a simple retry plan
                fallback_plan = [
                    {"description": f"Retry the failed operation: {original_objective}",
                     "command": failed_command}
                ]
                return ErrorRecoveryResponse(
                    content=json.dumps(fallback_plan),
                    recovery_plan=fallback_plan,
                    recovery_approach="simple_retry",
                    estimated_recovery_steps=1,
                    timestamp=asyncio.get_event_loop().time(),
                    success=False,
                    metadata={
                        "error": f"JSON parsing failed: {str(e)}",
                        "raw_response": response_text
                    }
                )

        except Exception as e:
            print(f"ERROR in ErrorRecoveryPlanner: {e}")

            # Create a fallback recovery plan
            fallback_plan = [
                {"description": f"Manual intervention required for: {original_objective}",
                 "command": "echo 'Recovery planning failed - manual intervention needed'"}
            ]
            return ErrorRecoveryResponse(
                content=json.dumps(fallback_plan),
                recovery_plan=fallback_plan,
                recovery_approach="manual_intervention",
                estimated_recovery_steps=1,
                timestamp=asyncio.get_event_loop().time(),
                success=False,
                metadata={
                    "error": f"ErrorRecoveryPlanner exception: {str(e)}",
                    "exception_type": type(e).__name__
                }
            )
